/**
  ******************************************************************************
  * @file    dac8552.c
  * <AUTHOR> 第二问项目组
  * @version V2.0
  * @date    2024
  * @brief   DAC8552双通道DAC驱动实现 (基于商家代码重写)
  ******************************************************************************
  */

#include "dac8552.h"

/**
 * @brief  DAC8552初始化函数
 * @param  None
 * @retval None
 * @note   配置GPIO引脚，使用GPIO模拟SPI时序 (参考商家代码)
 */
void DAC8552_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 1. 使能GPIOB时钟 (在BSP_Init中已使能)
    
    // 2. 配置DAC8552的GPIO引脚 (SYNC, SCK, MOSI)
    GPIO_InitStructure.GPIO_Pin = DAC8552_SYNC_PIN | DAC8552_SCK_PIN | DAC8552_MOSI_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;      // 推挽输出模式
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;  // 50MHz速度
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;     // 推挽输出
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;       // 上拉
    GPIO_Init(DAC8552_GPIO_PORT, &GPIO_InitStructure);
    
    // 3. 初始化引脚状态
    DAC8552_SYNC_H();  // SYNC默认高电平
    DAC8552_SCK_L();   // SCK默认低电平
    DAC8552_MOSI_L();  // MOSI默认低电平
    
    // 4. 初始化DAC8552寄存器 (完全按照商家代码)
    DAC8552_Write_Reg(0x000000 | 32768);  // 配置命令1 (商家代码)
    DAC8552_Write_Reg(0x340000 | 32768);  // 配置命令2 (商家代码)
}

/**
 * @brief  DAC8552写寄存器函数 (参考商家代码)
 * @param  data: 24位数据 (控制字节 + 16位DAC数据)
 * @retval None
 * @note   使用GPIO模拟SPI时序发送24位数据
 */
void DAC8552_Write_Reg(uint32_t data)
{
    uint8_t i;

    // 1. 拉低SYNC开始传输 (完全按照商家代码时序)
    DAC8552_SYNC_L();

    // SYNC建立时间延时
    for (volatile uint16_t delay = 0; delay < 200; delay++) {
        __NOP();
    }

    // 2. 发送24位数据，MSB先发送 (与商家代码完全一致)
    for (i = 0; i < 24; i++)
    {
        // 先拉高时钟 (商家时序：SCLK先高)
        DAC8552_SCK_H();

        // 根据数据位设置MOSI (商家时序：在SCLK高期间设置数据)
        if (data & 0x800000)  // 检查最高位
        {
            DAC8552_MOSI_H();
        }
        else
        {
            DAC8552_MOSI_L();
        }

        // 添加延时确保时序稳定 (增加延时)
        for (volatile uint16_t delay = 0; delay < 100; delay++) {
            __NOP();
        }

        // 拉低时钟 (商家时序：SCLK下降沿)
        DAC8552_SCK_L();

        // 添加延时确保时序稳定 (增加延时)
        for (volatile uint16_t delay = 0; delay < 100; delay++) {
            __NOP();
        }

        // 数据左移一位
        data <<= 1;
    }

    // SYNC保持时间延时
    for (volatile uint16_t delay = 0; delay < 200; delay++) {
        __NOP();
    }

    // 3. 拉高SYNC结束传输
    DAC8552_SYNC_H();

    // SYNC释放时间延时
    for (volatile uint16_t delay = 0; delay < 200; delay++) {
        __NOP();
    }
}

/**
 * @brief  DAC8552通道A写入函数
 * @param  data: 16位DAC数据
 * @retval None
 */
void DAC8552_Write_CHA(uint16_t data)
{
    // 发送通道A命令 + 数据
    DAC8552_Write_Reg(DAC8552_CMD_WRITE_A | data);
}

/**
 * @brief  DAC8552通道B写入函数
 * @param  data: 16位DAC数据
 * @retval None
 */
void DAC8552_Write_CHB(uint16_t data)
{
    // 发送通道B命令 + 数据
    DAC8552_Write_Reg(DAC8552_CMD_WRITE_B | data);
}

/**
 * @brief  DAC8552通用写入函数 (兼容性接口)
 * @param  channel: 通道选择 (DAC8552_CHANNEL_A 或 DAC8552_CHANNEL_B)
 * @param  data: 16位DAC数据
 * @retval None
 * @note   兼容原有接口，内部调用新的写入函数
 */
void DAC8552_Write(uint8_t channel, uint16_t data)
{
    // 根据通道选择调用对应的写入函数
    if (channel == DAC8552_CHANNEL_A)
    {
        DAC8552_Write_CHA(data);
    }
    else if (channel == DAC8552_CHANNEL_B)
    {
        DAC8552_Write_CHB(data);
    }
    // 参数错误时不执行任何操作
}
