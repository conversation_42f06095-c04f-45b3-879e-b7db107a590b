/**
  ******************************************************************************
  * @file    test_waveform.c
  * <AUTHOR> 第二问项目组
  * @version V1.0
  * @date    2024
  * @brief   波形测试程序 - 验证DAC8552输出
  ******************************************************************************
  */

#include "main.h"
#include "../Modules/Generation/dac8552.h"
#include "../Modules/Core/systick.h"
#include "bsp.h"

// 使用商家的正弦波表
extern const uint16_t sin_data[256];

/**
 * @brief  测试不同波形输出
 * @param  None
 * @retval None
 */
int test_waveform_main(void)
{
    uint16_t i;
    uint16_t test_value;
    
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();
    
    /* 初始化DAC8552 */
    DAC8552_Init();
    
    while (1)
    {
        // 测试1: 输出原始正弦波表 (满幅度)
        for (i = 0; i < 256; i++)
        {
            DAC8552_Write_CHA(sin_data[i]);
            // 可以添加少量延时来控制频率
            // for (volatile int j = 0; j < 10; j++);
        }
        
        // 测试2: 输出缩放后的正弦波 (0.8V峰峰值)
        for (i = 0; i < 256; i++)
        {
            int32_t temp = (int32_t)sin_data[i] - 0x7FFF;
            temp = temp * 0x147A / 0x7FFF;  // 缩放到±0.4V
            uint16_t scaled_value = (uint16_t)(temp + 0x7FFF);
            
            DAC8552_Write_CHA(scaled_value);
        }
        
        // 测试3: 输出固定电压 (验证DAC工作)
        DAC8552_Write_CHA(0x7FFF);  // 2.5V
        for (volatile int j = 0; j < 100000; j++);  // 延时
        
        DAC8552_Write_CHA(0x9999);  // 约3V
        for (volatile int j = 0; j < 100000; j++);  // 延时
        
        DAC8552_Write_CHA(0x6666);  // 约2V
        for (volatile int j = 0; j < 100000; j++);  // 延时
    }
}

/**
 * @brief  简单的方波测试
 * @param  None
 * @retval None
 */
void test_square_wave(void)
{
    while (1)
    {
        DAC8552_Write_CHA(0x8000 + 0x147A);  // 高电平 (2.9V)
        for (volatile int j = 0; j < 50000; j++);
        
        DAC8552_Write_CHA(0x8000 - 0x147A);  // 低电平 (2.1V)
        for (volatile int j = 0; j < 50000; j++);
    }
}

/**
 * @brief  三角波测试
 * @param  None
 * @retval None
 */
void test_triangle_wave(void)
{
    uint16_t value;
    
    while (1)
    {
        // 上升沿
        for (value = 0x7FFF - 0x147A; value < 0x7FFF + 0x147A; value += 0x100)
        {
            DAC8552_Write_CHA(value);
            for (volatile int j = 0; j < 1000; j++);
        }
        
        // 下降沿
        for (value = 0x7FFF + 0x147A; value > 0x7FFF - 0x147A; value -= 0x100)
        {
            DAC8552_Write_CHA(value);
            for (volatile int j = 0; j < 1000; j++);
        }
    }
}
