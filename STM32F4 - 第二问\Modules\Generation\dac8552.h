/**
  ******************************************************************************
  * @file    dac8552.h
  * <AUTHOR> 第二问项目组
  * @version V2.0
  * @date    2024
  * @brief   DAC8552双通道DAC驱动头文件 (基于商家代码重写)
  ******************************************************************************
  */

#ifndef __DAC8552_H
#define __DAC8552_H

#include "stm32f4xx.h"
#include "bsp.h"

/* DAC8552 GPIO控制宏定义 (参考商家代码) */
#define DAC8552_SYNC_L()    GPIO_ResetBits(DAC8552_GPIO_PORT, DAC8552_SYNC_PIN)
#define DAC8552_SYNC_H()    GPIO_SetBits(DAC8552_GPIO_PORT, DAC8552_SYNC_PIN)
#define DAC8552_SCK_L()     GPIO_ResetBits(DAC8552_GPIO_PORT, DAC8552_SCK_PIN)
#define DAC8552_SCK_H()     GPIO_SetBits(DAC8552_GPIO_PORT, DAC8552_SCK_PIN)
#define DAC8552_MOSI_L()    GPIO_ResetBits(DAC8552_GPIO_PORT, DAC8552_MOSI_PIN)
#define DAC8552_MOSI_H()    GPIO_SetBits(DAC8552_GPIO_PORT, DAC8552_MOSI_PIN)

/* DAC8552 通道定义 */
#define DAC8552_CHANNEL_A    0
#define DAC8552_CHANNEL_B    1

/* DAC8552 控制字节定义 (基于商家代码验证) */
#define DAC8552_CMD_WRITE_A  0x100000  // 写入通道A并更新输出 (24位: 0x10xxxx)
#define DAC8552_CMD_WRITE_B  0x240000  // 写入通道B并更新输出 (24位: 0x24xxxx)

/* DAC8552 最大输出值 (16位) */
#define DAC8552_MAX_VALUE    0xFFFF

/* 函数声明 */
void DAC8552_Init(void);
void DAC8552_Write_Reg(uint32_t data);
void DAC8552_Write_CHA(uint16_t data);
void DAC8552_Write_CHB(uint16_t data);
void DAC8552_Write(uint8_t channel, uint16_t data);

#endif /* __DAC8552_H */
