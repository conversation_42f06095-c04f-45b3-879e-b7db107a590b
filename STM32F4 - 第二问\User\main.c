/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> 第二问 正弦信号发生器
  * @version V2.0
  * @date    2024
  * @brief   STM32F4控制DAC8552产生固定3kHz、0.8V峰峰值正弦信号
  *          基于商家示例代码重写，使用GPIO模拟SPI时序
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
// #include "../Modules/Core/usart.h"  // 注释掉串口，只用示波器观察
#include "bsp.h"

// 第二问专用：DAC8552正弦信号生成
#include "../Modules/Generation/dac8552.h"     // DAC8552双通道DAC驱动

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// 基于商家代码的简化实现，不需要复杂的正弦波生成器结构

// 正弦波查找表 (256点，基于商家代码)
const uint16_t sin_data[256] = {
    0x7FFF,0x8323,0x8647,0x896A,0x8C8B,0x8FAA,0x92C7,0x95E1,0x98F8,0x9C0A,0x9F19,0xA223,0xA527,0xA826,0xAB1E,0xAE10,
    0xB0FB,0xB3DE,0xB6B9,0xB98C,0xBC55,0xBF16,0xC1CD,0xC47A,0xC71C,0xC9B3,0xCC3F,0xCEBF,0xD132,0xD39A,0xD5F4,0xD842,
    0xDA81,0xDCB3,0xDED6,0xE0EB,0xE2F1,0xE4E7,0xE6CE,0xE8A5,0xEA6C,0xEC23,0xEDC9,0xEF5E,0xF0E1,0xF254,0xF3B4,0xF503,
    0xF640,0xF76B,0xF883,0xF989,0xFA7C,0xFB5C,0xFC28,0xFCE2,0xFD89,0xFE1C,0xFE9C,0xFF08,0xFF61,0xFFA6,0xFFD7,0xFFF5,
    0xFFFE,0xFFF5,0xFFD7,0xFFA6,0xFF61,0xFF08,0xFE9C,0xFE1C,0xFD89,0xFCE2,0xFC29,0xFB5C,0xFA7C,0xF989,0xF883,0xF76B,
    0xF640,0xF503,0xF3B4,0xF254,0xF0E1,0xEF5E,0xEDC9,0xEC23,0xEA6C,0xE8A5,0xE6CE,0xE4E7,0xE2F1,0xE0EB,0xDED6,0xDCB3,
    0xDA81,0xD842,0xD5F4,0xD39A,0xD132,0xCEBF,0xCC3F,0xC9B3,0xC71C,0xC47A,0xC1CD,0xBF16,0xBC55,0xB98C,0xB6B9,0xB3DE,
    0xB0FB,0xAE10,0xAB1E,0xA826,0xA527,0xA222,0x9F19,0x9C0A,0x98F8,0x95E1,0x92C7,0x8FAA,0x8C8B,0x8969,0x8647,0x8323,
    0x7FFF,0x7CDB,0x79B7,0x7694,0x7373,0x7054,0x6D37,0x6A1D,0x6706,0x63F4,0x60E5,0x5DDB,0x5AD7,0x57D8,0x54E0,0x51EE,
    0x4F03,0x4C20,0x4945,0x4672,0x43A8,0x40E8,0x3E31,0x3B84,0x38E2,0x364B,0x33BF,0x313F,0x2ECB,0x2C64,0x2A0A,0x27BC,
    0x257D,0x234B,0x2128,0x1F13,0x1D0D,0x1B17,0x1930,0x1759,0x1592,0x13DB,0x1235,0x10A0,0x0F1D,0x0DAA,0x0C49,0x0AFB,
    0x09BE,0x0893,0x077B,0x0675,0x0582,0x04A2,0x03D5,0x031C,0x0275,0x01E2,0x0162,0x00F6,0x009D,0x0058,0x0027,0x0009,
    0x0000,0x0009,0x0027,0x0058,0x009D,0x00F6,0x0162,0x01E2,0x0275,0x031C,0x03D6,0x04A3,0x0583,0x0675,0x077B,0x0893,
    0x09BE,0x0AFB,0x0C4A,0x0DAA,0x0F1D,0x10A1,0x1236,0x13DB,0x1592,0x1759,0x1930,0x1B17,0x1D0E,0x1F13,0x2128,0x234B,
    0x257D,0x27BD,0x2A0A,0x2C64,0x2ECC,0x3140,0x33C0,0x364B,0x38E3,0x3B85,0x3E31,0x40E8,0x43A9,0x4673,0x4945,0x4C21,
    0x4F04,0x51EE,0x54E0,0x57D9,0x5AD7,0x5DDC,0x60E5,0x63F4,0x6707,0x6A1D,0x6D37,0x7054,0x7374,0x7695,0x79B8,0x7CDB
};

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    // USART1_Init(115200);  // 注释掉串口初始化
    BSP_Init();

    /* 初始化DAC8552 - 这会配置PB12, PB13, PB15 */
    DAC8552_Init();

    // 测试LED引脚 - 确认程序是否运行
    GPIO_InitTypeDef GPIO_InitStructure;

    // 配置LED引脚 (通常是PC13或其他引脚)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_13;  // PC13通常是LED
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOC, &GPIO_InitStructure);

    // 配置调试引脚 PB14
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_14;  // PB14用于调试信号
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOB, &GPIO_InitStructure);

    while (1)
    {
        // 调试信号：开始DAC通信 (改用PB14避免冲突)
        GPIO_SetBits(GPIOB, GPIO_Pin_14);    // PB14高电平标记开始

        // 测试DAC8552输出固定值
        DAC8552_Write_CHA(0xFFFF);  // 最大值，应该输出接近5V

        // 调试信号：DAC通信结束
        GPIO_ResetBits(GPIOB, GPIO_Pin_14);  // PB14低电平标记结束

        // 延时
        volatile uint32_t i;
        for (i = 0; i < 2000000; i++) {
            __NOP();
        }

        // 调试信号：开始DAC通信
        GPIO_SetBits(GPIOB, GPIO_Pin_14);    // PB14高电平标记开始

        DAC8552_Write_CHA(0x0000);  // 最小值，应该输出接近0V

        // 调试信号：DAC通信结束
        GPIO_ResetBits(GPIOB, GPIO_Pin_14);  // PB14低电平标记结束

        // 延时
        for (i = 0; i < 2000000; i++) {
            __NOP();
        }

        // 闪烁LED确认程序运行
        GPIO_ToggleBits(GPIOC, GPIO_Pin_13);
    }
}

// ==================== 基于商家代码的简化实现 ====================
// 直接在主循环中使用正弦波表，无需复杂的生成器结构

/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 系统时钟已经在SystemInit()中配置为168MHz */
    /* 这里可以添加额外的时钟配置代码 */
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

/**
  * @brief  TIM6中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void DDS_TIM6_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用定时器中断来更新DDS
    // 我们在主循环中直接调用SineGen_Update()
}

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


