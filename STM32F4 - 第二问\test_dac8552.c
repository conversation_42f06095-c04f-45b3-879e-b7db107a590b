/**
  ******************************************************************************
  * @file    test_dac8552.c
  * <AUTHOR> 第二问项目组
  * @version V1.0
  * @date    2024
  * @brief   DAC8552驱动测试程序
  ******************************************************************************
  */

#include "main.h"
#include "../Modules/Generation/dac8552.h"
#include "../Modules/Core/systick.h"
#include "bsp.h"

/**
 * @brief  DAC8552驱动测试主函数
 * @param  None
 * @retval None
 */
int test_dac8552_main(void)
{
    uint16_t i;
    uint16_t test_value;
    
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();
    
    /* 初始化DAC8552 */
    DAC8552_Init();
    
    /* 测试1: 输出固定电压 */
    // 通道A输出1.65V (中间值)
    DAC8552_Write_CHA(32768);
    
    // 通道B输出2.5V
    DAC8552_Write_CHB(32768 * 2.5 / 5.0);
    
    Delay_ms(2000);  // 等待2秒
    
    /* 测试2: 输出斜坡波 */
    for (i = 0; i < 1000; i++)
    {
        test_value = i * 65;  // 0到65000的斜坡
        DAC8552_Write_CHA(test_value);
        DAC8552_Write_CHB(65535 - test_value);  // 反向斜坡
        Delay_ms(10);
    }
    
    /* 测试3: 输出简单正弦波 */
    while (1)
    {
        for (i = 0; i < 256; i++)
        {
            // 使用main.c中的正弦波表
            extern const uint16_t sine_table[256];
            uint16_t sine_value = sine_table[i];
            
            // 将12位值扩展到16位
            sine_value = sine_value << 4;
            
            DAC8552_Write_CHA(sine_value);
            DAC8552_Write_CHB(sine_value);
            
            Delay_ms(1);  // 约1kHz正弦波
        }
    }
}

/**
 * @brief  简单延时函数
 * @param  ms: 延时毫秒数
 * @retval None
 */
void Delay_ms(uint32_t ms)
{
    uint32_t start_time = SysTick_GetTick();
    while ((SysTick_GetTick() - start_time) < ms)
    {
        // 等待
    }
}
